<?php
session_start();
if (isset($_SESSION['user_id'])) {
  header("Location: dashboard.php");
  exit;
}

require_once __DIR__ . '/includes/attendance_helpers.php';
require_once __DIR__ . '/includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("DB connection failed.");
}

$error = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  $username = trim($_POST['username'] ?? '');
  $password = $_POST['password'] ?? '';

  if ($username && $password) {
    // Updated query to work with new role system
    $stmt = $mysqli->prepare("SELECT id, password_hash, employee_id FROM users WHERE username = ?");
    $stmt->bind_param('s', $username);
    $stmt->execute();
    $stmt->bind_result($id, $password_hash, $employee_id);
    if ($stmt->fetch()) {
      $stmt->close();

      if (password_verify($password, $password_hash)) {
        $_SESSION['user_id'] = $id;
        $_SESSION['username'] = $username;
        $_SESSION['employee_id'] = $employee_id;

        // Get user roles and permissions
        $user_roles = getUserRoles($mysqli, $id);
        $user_permissions = getUserPermissions($mysqli, $id);

        // Store permissions in session for quick access
        $_SESSION['permissions'] = $user_permissions;
        $_SESSION['roles'] = array_column($user_roles, 'name');

        // Determine redirect logic based on roles and permissions
        $is_admin = hasRole($mysqli, $id, 'super_admin') || hasRole($mysqli, $id, 'admin');
        $can_manage = hasAnyPermission($mysqli, $id, ['employees.create', 'shifts.create', 'roles.view']);

        if ($is_admin || $can_manage) {
          // Admin or management users go directly to dashboard
          header("Location: dashboard.php");
          exit;
        } else {
          // Regular employee users: check if they've checked in today
          if ($employee_id && hasCheckedInToday($mysqli, $employee_id)) {
            // Already checked in today, go to dashboard
            header("Location: dashboard.php");
            exit;
          } else {
            // Haven't checked in today, go to check-in page
            header("Location: index.php");
            exit;
          }
        }
      } else {
        $error = "Invalid username or password.";
      }
    } else {
      $error = "Invalid username or password.";
    }
  } else {
    $error = "Please enter username and password.";
  }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Login - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <div class="page-container d-flex align-items-center justify-content-center" style="min-height: 100vh;">
    <div class="content-card" style="max-width: 400px; width: 100%;">
      <!-- Login Header -->
      <div class="text-center mb-4">
        <div class="login-icon mb-3">
          <i class="bi bi-shield-lock"></i>
        </div>
        <h1 class="page-title mb-2">Welcome Back</h1>
        <p class="page-subtitle">Sign in to your attendance account</p>
      </div>

      <!-- Error Message -->
      <?php if ($error): ?>
        <div class="alert alert-danger">
          <i class="bi bi-exclamation-triangle me-2"></i>
          <?= htmlspecialchars($error) ?>
        </div>
      <?php endif; ?>

      <!-- Login Form -->
      <form method="POST" action="login.php" novalidate>
        <div class="form-group">
          <label for="username" class="form-label">
            <i class="bi bi-person me-2"></i>
            Username
          </label>
          <input
            type="text"
            class="form-control"
            name="username"
            id="username"
            required
            autofocus
            placeholder="Enter your username"
            value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
          />
        </div>

        <div class="form-group">
          <label for="password" class="form-label">
            <i class="bi bi-lock me-2"></i>
            Password
          </label>
          <input
            type="password"
            class="form-control"
            name="password"
            id="password"
            required
            placeholder="Enter your password"
          />
        </div>

        <button type="submit" class="btn btn-primary w-100">
          <i class="bi bi-box-arrow-in-right me-2"></i>
          Sign In
        </button>
      </form>

      <!-- Login Info -->
      <div class="mt-4 p-3 bg-light rounded">
        <h6 class="mb-2">Demo Credentials:</h6>
        <small class="text-muted">
          <strong>Admin:</strong> username: admin, password: admin<br>
          <strong>Employee:</strong> Create employee accounts through admin panel
        </small>
      </div>
    </div>
  </div>

  <style>
    .login-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: var(--primary-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      margin: 0 auto;
      box-shadow: var(--shadow-lg);
    }

    .page-container {
      padding: 2rem;
    }

    .content-card {
      box-shadow: var(--shadow-xl);
    }

    @media (max-width: 480px) {
      .page-container {
        padding: 1rem;
      }

      .login-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
      }
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
