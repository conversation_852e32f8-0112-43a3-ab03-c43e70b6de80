<?php
session_start();
if (!isset($_SESSION['user_id'])) {
  header('HTTP/1.1 403 Forbidden');
  die(json_encode([]));
}

// Include permissions system
require_once __DIR__ . '/includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  echo json_encode([]);
  exit;
}

$user_id = $_SESSION['user_id'];
$employee_id_session = $_SESSION['employee_id'] ?? 0;

// Check if user can view all employees or just their own
if (hasPermission($mysqli, $user_id, 'employees.view') || hasPermission($mysqli, $user_id, 'employees.edit')) {
  // User can view all employees
  $result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
  $employees = [];
  while ($row = $result->fetch_assoc()) {
    $employees[] = $row;
  }
} else {
  // User can only view their own employee record
  $stmt = $mysqli->prepare("SELECT id, name FROM employees WHERE id = ?");
  $stmt->bind_param("i", $employee_id_session);
  $stmt->execute();
  $result = $stmt->get_result();
  $employees = $result->fetch_all(MYSQLI_ASSOC);
  $stmt->close();
}

header('Content-Type: application/json');
echo json_encode($employees);
?>
