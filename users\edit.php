<?php
session_start();

// Include configuration and permissions system
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require user edit permissions
requirePermission($mysqli, 'users.edit');

$msg = "";
$error = "";
$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$user_id) {
  header("Location: " . appUrl('users/list.php'));
  exit;
}

// Get user account information
$user_stmt = $mysqli->prepare("
  SELECT u.id, u.username, u.employee_id, u.role,
         e.name as employee_name, e.email as employee_email, e.department, e.position
  FROM users u
  LEFT JOIN employees e ON u.employee_id = e.id
  WHERE u.id = ?
");
$user_stmt->bind_param('i', $user_id);
$user_stmt->execute();
$user_result = $user_stmt->get_result();
$user_account = $user_result->fetch_assoc();
$user_stmt->close();

if (!$user_account) {
  $_SESSION['error_message'] = "User account not found.";
  header("Location: " . appUrl('users/list.php'));
  exit;
}

// Use functions from permissions.php - getUserRoles() and getAllRoles() are already defined there

// Check if roles table exists before calling getUserRoles
$tables_check = $mysqli->query("SHOW TABLES LIKE 'roles'");
if ($tables_check->num_rows == 0) {
    // Roles table doesn't exist, use empty array
    $user_role_objects = [];
    $all_roles = [];
    $error = "The roles and permissions system has not been set up yet. Please run the database migration script.";
} else {
    $user_role_objects = getUserRoles($mysqli, $user_id);
    $all_roles = getAllRoles($mysqli);
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['update_user_credentials'])) {
    // Handle user credentials update
    $username = trim($_POST['username'] ?? '');
    $new_password = trim($_POST['new_password'] ?? '');
    $confirm_password = trim($_POST['confirm_password'] ?? '');
    $assigned_roles = $_POST['roles'] ?? [];

    if ($username) {
      try {
        // Check if username is unique (excluding current user)
        $username_check = $mysqli->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
        $username_check->bind_param('si', $username, $user_id);
        $username_check->execute();
        $username_result = $username_check->get_result();

        if ($username_result->num_rows > 0) {
          $error = "Username is already in use by another user.";
        }
        $username_check->close();

        // Validate password if provided
        if ($new_password && $new_password !== $confirm_password) {
          $error = "Password confirmation does not match.";
        }

        if (!$error) {
          $mysqli->begin_transaction();

          // Update user basic info
          if ($new_password) {
            $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
            $update_user_stmt = $mysqli->prepare("UPDATE users SET username = ?, password_hash = ? WHERE id = ?");
            $update_user_stmt->bind_param('ssi', $username, $password_hash, $user_id);
          } else {
            $update_user_stmt = $mysqli->prepare("UPDATE users SET username = ? WHERE id = ?");
            $update_user_stmt->bind_param('si', $username, $user_id);
          }

          if (!$update_user_stmt->execute()) {
            throw new Exception("Failed to update user credentials.");
          }
          $update_user_stmt->close();

          // Update user roles if user has permission
          if (hasPermission($mysqli, $_SESSION['user_id'], 'roles.assign')) {
            // Remove existing roles
            $delete_roles_stmt = $mysqli->prepare("DELETE FROM user_roles WHERE user_id = ?");
            $delete_roles_stmt->bind_param('i', $user_id);
            $delete_roles_stmt->execute();
            $delete_roles_stmt->close();

            // Add new roles
            if (!empty($assigned_roles)) {
              $insert_role_stmt = $mysqli->prepare("INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)");
              foreach ($assigned_roles as $role_id) {
                $insert_role_stmt->bind_param('ii', $user_id, $role_id);
                $insert_role_stmt->execute();
              }
              $insert_role_stmt->close();
            }
          }

          $mysqli->commit();
          $msg = "User account updated successfully.";

          // Refresh user account data
          $refresh_user_stmt = $mysqli->prepare("
            SELECT u.id, u.username, u.employee_id, u.role,
                   e.name as employee_name, e.email as employee_email, e.department, e.position
            FROM users u
            LEFT JOIN employees e ON u.employee_id = e.id
            WHERE u.id = ?
          ");
          $refresh_user_stmt->bind_param('i', $user_id);
          $refresh_user_stmt->execute();
          $refresh_user_result = $refresh_user_stmt->get_result();
          $user_account = $refresh_user_result->fetch_assoc();
          $refresh_user_stmt->close();

          // Refresh user roles
          $user_role_objects = getUserRoles($mysqli, $user_id);

        } else {
          $mysqli->rollback();
        }
      } catch (Exception $e) {
        $mysqli->rollback();
        $error = "Error updating user account: " . $e->getMessage();
      }
    } else {
      $error = "Username is required.";
    }
  }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Edit User Account - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include __DIR__ . '/../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Edit User Account</h1>
          <p class="page-subtitle">Manage user credentials and system access</p>
        </div>
        <div class="d-flex gap-3">
          <?php if ($user_account['employee_id'] && hasPermission($mysqli, $_SESSION['user_id'], 'employees.edit')): ?>
            <a href="<?= appUrl('employees/edit.php?id=' . $user_account['employee_id']) ?>" class="btn btn-secondary">
              <i class="bi bi-person-gear me-2"></i>
              Edit Employee Details
            </a>
          <?php endif; ?>
          <a href="<?= appUrl('users/list.php') ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to User List
          </a>
        </div>
      </div>
    </div>

    <!-- Alert Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <?php endif; ?>

    <!-- User Account Information -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-person-badge me-2"></i>
        User Account Information
      </h2>

      <!-- Employee Link (if exists) -->
      <?php if ($user_account['employee_id']): ?>
        <div class="row mb-3">
          <div class="col-12">
            <div class="alert alert-info">
              <i class="bi bi-link-45deg me-2"></i>
              <strong>Linked Employee:</strong> <?= htmlspecialchars($user_account['employee_name']) ?>
              <?php if ($user_account['employee_email']): ?>
                (<?= htmlspecialchars($user_account['employee_email']) ?>)
              <?php endif; ?>
              <?php if ($user_account['department']): ?>
                - <?= htmlspecialchars($user_account['department']) ?>
              <?php endif; ?>
              <?php if ($user_account['position']): ?>
                - <?= htmlspecialchars($user_account['position']) ?>
              <?php endif; ?>
            </div>
          </div>
        </div>
      <?php endif; ?>

      <!-- User Credentials Form -->
      <form method="POST" id="userCredentialsForm">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="username" class="form-label">
                <i class="bi bi-person me-2"></i>
                Username *
              </label>
              <input
                type="text"
                class="form-control"
                name="username"
                id="username"
                required
                placeholder="Enter username"
                value="<?= htmlspecialchars($user_account['username']) ?>"
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label text-muted">
                <i class="bi bi-archive me-2"></i>
                Legacy Role (Deprecated)
              </label>
              <div class="form-control-plaintext">
                <span class="badge bg-secondary bg-opacity-10 text-secondary border border-secondary border-opacity-25">
                  <?= ucfirst($user_account['role']) ?>
                </span>
                <small class="text-muted d-block">
                  <i class="bi bi-info-circle me-1"></i>
                  This legacy role is kept for backward compatibility. The system now uses the "Current System Roles" below for all permissions.
                </small>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="new_password" class="form-label">
                <i class="bi bi-key me-2"></i>
                New Password
              </label>
              <input
                type="password"
                class="form-control"
                name="new_password"
                id="new_password"
                placeholder="Leave blank to keep current password"
              />
              <small class="form-text text-muted">Leave blank to keep current password</small>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="confirm_password" class="form-label">
                <i class="bi bi-key me-2"></i>
                Confirm New Password
              </label>
              <input
                type="password"
                class="form-control"
                name="confirm_password"
                id="confirm_password"
                placeholder="Confirm new password"
              />
            </div>
          </div>
        </div>

        <!-- Current System Roles Display -->
        <div class="form-group">
          <label class="form-label">
            <i class="bi bi-shield-check me-2"></i>
            Current System Roles
          </label>
          <div class="d-flex flex-wrap gap-2 mb-3">
            <?php if (!empty($user_role_objects)): ?>
              <?php foreach ($user_role_objects as $role): ?>
                <span class="badge bg-success bg-opacity-10 text-success border border-success border-opacity-25 fs-6 px-3 py-2">
                  <i class="bi bi-check-circle me-1"></i>
                  <?= htmlspecialchars($role['display_name']) ?>
                </span>
              <?php endforeach; ?>
            <?php else: ?>
              <span class="badge bg-warning bg-opacity-10 text-warning border border-warning border-opacity-25 fs-6 px-3 py-2">
                <i class="bi bi-exclamation-triangle me-1"></i>
                No roles assigned
              </span>
            <?php endif; ?>
          </div>
        </div>

        <!-- Role Assignment (if user has permission) -->
        <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'roles.assign')): ?>
          <div class="form-group">
            <label class="form-label">
              <i class="bi bi-gear me-2"></i>
              Assign/Remove System Roles
            </label>
            <small class="text-muted d-block mb-3">Check or uncheck roles to assign or remove them from this user account.</small>
            <div class="row">
              <?php foreach ($all_roles as $role): ?>
                <?php $is_assigned = in_array($role['id'], array_column($user_role_objects, 'id')); ?>
                <div class="col-md-6 col-lg-4 mb-2">
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      name="roles[]"
                      value="<?= $role['id'] ?>"
                      id="role_<?= $role['id'] ?>"
                      <?= $is_assigned ? 'checked' : '' ?>
                    />
                    <label class="form-check-label" for="role_<?= $role['id'] ?>">
                      <strong class="<?= $is_assigned ? 'text-success' : '' ?>">
                        <?php if ($is_assigned): ?>
                          <i class="bi bi-check-circle me-1"></i>
                        <?php endif; ?>
                        <?= htmlspecialchars($role['display_name']) ?>
                      </strong>
                      <?php if ($role['description']): ?>
                        <br><small class="text-muted"><?= htmlspecialchars($role['description']) ?></small>
                      <?php endif; ?>
                    </label>
                  </div>
                </div>
              <?php endforeach; ?>
            </div>
          </div>
        <?php endif; ?>

        <div class="d-flex gap-3 mt-4">
          <button type="submit" name="update_user_credentials" class="btn btn-primary">
            <i class="bi bi-check-circle me-2"></i>
            Update User Account
          </button>
          <a href="<?= appUrl('users/list.php') ?>" class="btn btn-secondary">
            <i class="bi bi-x-circle me-2"></i>
            Cancel
          </a>
        </div>
      </form>
    </div>

    <!-- Account Information -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-info-circle me-2"></i>
        Account Information
      </h2>
      <div class="row">
        <div class="col-md-6">
          <small class="text-muted">User ID</small>
          <div class="fw-semibold"><?= $user_account['id'] ?></div>
        </div>
        <div class="col-md-6">
          <small class="text-muted">Legacy Role (Deprecated)</small>
          <div class="fw-semibold text-muted"><?= ucfirst($user_account['role']) ?></div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Form validation
    document.getElementById('userCredentialsForm').addEventListener('submit', function(e) {
      const username = document.getElementById('username').value.trim();
      const newPassword = document.getElementById('new_password').value;
      const confirmPassword = document.getElementById('confirm_password').value;

      if (!username) {
        e.preventDefault();
        alert('Username is required.');
        document.getElementById('username').focus();
        return false;
      }

      if (newPassword && newPassword !== confirmPassword) {
        e.preventDefault();
        alert('Password confirmation does not match.');
        document.getElementById('confirm_password').focus();
        return false;
      }
    });
  </script>
</body>
</html>
