<?php
session_start();

// Include configuration and permissions system
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require user view permissions
requirePermission($mysqli, 'users.view');

$msg = "";
$error = "";

// Handle user deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_user'])) {
  $user_id = intval($_POST['user_id']);

  if ($user_id && hasPermission($mysqli, $_SESSION['user_id'], 'users.delete')) {
    // Don't allow deleting yourself
    if ($user_id === $_SESSION['user_id']) {
      $error = "You cannot delete your own account.";
    } else {
      $mysqli->begin_transaction();

      try {
        // Delete user roles first
        $roles_stmt = $mysqli->prepare("DELETE FROM user_roles WHERE user_id = ?");
        $roles_stmt->bind_param('i', $user_id);
        $roles_stmt->execute();
        $roles_stmt->close();

        // Delete user account
        $user_stmt = $mysqli->prepare("DELETE FROM users WHERE id = ?");
        $user_stmt->bind_param('i', $user_id);

        if ($user_stmt->execute()) {
          $user_stmt->close();
          $mysqli->commit();
          $msg = "User account deleted successfully.";
        } else {
          throw new Exception("Failed to delete user account.");
        }
      } catch (Exception $e) {
        $mysqli->rollback();
        $error = $e->getMessage();
      }
    }
  }
}

// Fetch all users with employee information
$users_query = "
  SELECT u.*, e.name as employee_name, e.email as employee_email, e.department, e.position,
         e.hire_date, e.created_at as employee_created_at,
         GROUP_CONCAT(r.display_name SEPARATOR ', ') as roles
  FROM users u
  LEFT JOIN employees e ON u.employee_id = e.id
  LEFT JOIN user_roles ur ON u.id = ur.user_id
  LEFT JOIN roles r ON ur.role_id = r.id
  GROUP BY u.id
  ORDER BY u.username
";
$users_result = $mysqli->query($users_query);
$users = $users_result->fetch_all(MYSQLI_ASSOC);

// Set default status for all users since it doesn't exist in current schema
foreach ($users as &$user) {
  if (!isset($user['status'])) {
    $user['status'] = 'active';
  }
}

// Get unique departments for filtering
$departments_query = "SELECT DISTINCT department FROM employees WHERE department IS NOT NULL AND department != '' ORDER BY department";
$departments_result = $mysqli->query($departments_query);
$departments = $departments_result->fetch_all(MYSQLI_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>User Accounts - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Filters and Search -->
    <div class="content-card">
      <div class="row align-items-end">
        <div class="col-md-4">
          <label for="searchInput" class="form-label">
            <i class="bi bi-search me-2"></i>
            Search Users
          </label>
          <input
            type="text"
            class="form-control"
            id="searchInput"
            placeholder="Search by username, employee name, or email..."
            onkeyup="filterUsers()"
          />
        </div>
        <div class="col-md-3">
          <label for="statusFilter" class="form-label">
            <i class="bi bi-funnel me-2"></i>
            Filter by Status
          </label>
          <select class="form-select" id="statusFilter" onchange="filterUsers()">
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="suspended">Suspended</option>
          </select>
        </div>
        <div class="col-md-3">
          <label for="departmentFilter" class="form-label">
            <i class="bi bi-building me-2"></i>
            Filter by Department
          </label>
          <select class="form-select" id="departmentFilter" onchange="filterUsers()">
            <option value="">All Departments</option>
            <?php foreach ($departments as $dept): ?>
              <option value="<?= strtolower(htmlspecialchars($dept['department'])) ?>">
                <?= htmlspecialchars($dept['department']) ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="col-md-2">
          <div class="text-center">
            <small class="text-muted">Total Users</small>
            <div class="fw-bold fs-4" id="userCount"><?= count($users) ?></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Users List -->
    <div class="content-card">

      <?php if (count($users) === 0): ?>
        <div class="text-center py-5">
          <i class="bi bi-person-x" style="font-size: 3rem; color: var(--text-secondary);"></i>
          <p class="mt-3 text-muted">No user accounts found.</p>
          <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'employees.view')): ?>
            <a href="<?= appUrl('employees/list.php') ?>" class="btn btn-primary">
              <i class="bi bi-people me-2"></i>
              Manage Employees
            </a>
          <?php endif; ?>
        </div>
      <?php else: ?>
        <div class="table-container">
          <!-- Desktop Table View -->
          <table class="table d-none d-lg-table" id="userTable">
            <thead>
              <tr>
                <th>
                  <i class="bi bi-person me-2"></i>
                  User Account
                </th>
                <th>
                  <i class="bi bi-person-badge me-2"></i>
                  Employee Details
                </th>
                <th>
                  <i class="bi bi-shield-lock me-2"></i>
                  Status & System Access
                </th>
                <th width="150">
                  <i class="bi bi-gear me-2"></i>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              <?php foreach ($users as $user): ?>
                <tr class="user-row"
                    data-username="<?= strtolower(htmlspecialchars($user['username'])) ?>"
                    data-employee="<?= strtolower(htmlspecialchars($user['employee_name'] ?? '')) ?>"
                    data-email="<?= strtolower(htmlspecialchars($user['employee_email'] ?? '')) ?>"
                    data-department="<?= strtolower(htmlspecialchars($user['department'] ?? '')) ?>"
                    data-status="<?= strtolower($user['status']) ?>">
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar me-3">
                        <?= strtoupper(substr($user['username'], 0, 1)) ?>
                      </div>
                      <div>
                        <div class="fw-bold text-primary"><?= htmlspecialchars($user['username']) ?></div>
                        <small class="text-muted d-block">ID: <?= $user['id'] ?></small>
                        <?php if ($user['employee_created_at']): ?>
                          <small class="text-muted d-block">
                            <i class="bi bi-calendar-plus me-1"></i>
                            Joined: <?= date('M j, Y', strtotime($user['employee_created_at'])) ?>
                          </small>
                        <?php elseif ($user['hire_date']): ?>
                          <small class="text-muted d-block">
                            <i class="bi bi-calendar-plus me-1"></i>
                            Hired: <?= date('M j, Y', strtotime($user['hire_date'])) ?>
                          </small>
                        <?php else: ?>
                          <small class="text-muted d-block">
                            <i class="bi bi-calendar-plus me-1"></i>
                            Date: Not available
                          </small>
                        <?php endif; ?>
                      </div>
                    </div>
                  </td>
                  <td>
                    <?php if ($user['employee_name']): ?>
                      <div class="fw-bold text-dark"><?= htmlspecialchars($user['employee_name']) ?></div>
                      <?php if ($user['employee_email']): ?>
                        <small class="text-muted d-block">
                          <i class="bi bi-envelope me-1"></i>
                          <?= htmlspecialchars($user['employee_email']) ?>
                        </small>
                      <?php endif; ?>
                      <div class="mt-1">
                        <?php if ($user['department']): ?>
                          <span class="badge bg-primary bg-opacity-10 text-primary border border-primary border-opacity-25 me-1">
                            <i class="bi bi-building me-1"></i>
                            <?= htmlspecialchars($user['department']) ?>
                          </span>
                        <?php endif; ?>
                        <?php if ($user['position']): ?>
                          <span class="badge bg-secondary bg-opacity-10 text-secondary border border-secondary border-opacity-25">
                            <i class="bi bi-briefcase me-1"></i>
                            <?= htmlspecialchars($user['position']) ?>
                          </span>
                        <?php endif; ?>
                      </div>
                    <?php else: ?>
                      <div class="text-center py-2">
                        <i class="bi bi-person-dash text-muted" style="font-size: 1.5rem;"></i>
                        <div class="text-muted small">No employee linked</div>
                      </div>
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php
                    $status_class = match($user['status']) {
                      'active' => 'success',
                      'inactive' => 'warning',
                      'suspended' => 'danger',
                      default => 'secondary'
                    };
                    $status_icon = match($user['status']) {
                      'active' => 'check-circle-fill',
                      'inactive' => 'pause-circle-fill',
                      'suspended' => 'x-circle-fill',
                      default => 'question-circle-fill'
                    };
                    $status_text = match($user['status']) {
                      'active' => 'Active',
                      'inactive' => 'Inactive',
                      'suspended' => 'Suspended',
                      default => 'Unknown'
                    };
                    ?>
                    <div class="mb-2">
                      <span class="badge bg-<?= $status_class ?> px-2 py-1">
                        <i class="bi bi-<?= $status_icon ?> me-1"></i>
                        <?= $status_text ?>
                      </span>
                    </div>

                    <?php if ($user['roles']): ?>
                      <div class="roles-container">
                        <small class="text-muted d-block mb-1">
                          <i class="bi bi-shield-lock me-1"></i>
                          <strong>System Access:</strong>
                        </small>
                        <?php
                        $roles_array = explode(', ', $user['roles']);
                        foreach ($roles_array as $role):
                        ?>
                          <span class="badge bg-info bg-opacity-10 text-info border border-info border-opacity-25 me-1 mb-1">
                            <i class="bi bi-shield-lock me-1"></i>
                            <?= htmlspecialchars(trim($role)) ?>
                          </span>
                        <?php endforeach; ?>
                      </div>
                    <?php else: ?>
                      <small class="text-muted">
                        <i class="bi bi-exclamation-triangle me-1"></i>
                        No system access assigned
                      </small>
                    <?php endif; ?>
                  </td>
                  <td>
                    <div class="d-flex flex-column gap-1">
                      <?php if ($user['employee_id']): ?>
                        <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'users.edit')): ?>
                          <a href="<?= appUrl('users/edit.php?id=' . $user['id']) ?>"
                             class="btn btn-primary btn-sm d-flex align-items-center justify-content-center"
                             title="Edit user account and credentials">
                            <i class="bi bi-key me-1"></i>
                            <span class="d-none d-lg-inline">Edit Account</span>
                          </a>
                        <?php endif; ?>

                        <?php if ($user['employee_id'] && hasPermission($mysqli, $_SESSION['user_id'], 'employees.edit')): ?>
                          <a href="<?= appUrl('employees/edit.php?id=' . $user['employee_id']) ?>"
                             class="btn btn-secondary btn-sm d-flex align-items-center justify-content-center"
                             title="Edit employee details">
                            <i class="bi bi-person-gear me-1"></i>
                            <span class="d-none d-lg-inline">Edit Employee</span>
                          </a>
                        <?php elseif ($user['employee_id'] && hasPermission($mysqli, $_SESSION['user_id'], 'employees.view')): ?>
                          <a href="<?= appUrl('employees/edit.php?id=' . $user['employee_id']) ?>"
                             class="btn btn-info btn-sm d-flex align-items-center justify-content-center"
                             title="View employee profile">
                            <i class="bi bi-eye me-1"></i>
                            <span class="d-none d-lg-inline">View Employee</span>
                          </a>
                        <?php endif; ?>

                        <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'users.delete') && $user['id'] !== $_SESSION['user_id']): ?>
                          <form method="POST" class="d-inline">
                            <input type="hidden" name="user_id" value="<?= $user['id'] ?>" />
                            <button type="submit" name="delete_user"
                                    class="btn btn-danger btn-sm d-flex align-items-center justify-content-center w-100"
                                    title="Delete user account"
                                    onclick="return confirm('⚠️ Delete User Account\n\nAre you sure you want to delete user \'<?= htmlspecialchars($user['username']) ?>\'?\n\n• This will permanently remove the user account\n• Employee record will remain intact\n• This action cannot be undone\n\nClick OK to proceed or Cancel to abort.');">
                              <i class="bi bi-trash me-1"></i>
                              <span class="d-none d-lg-inline">Delete</span>
                            </button>
                          </form>
                        <?php endif; ?>
                      <?php else: ?>
                        <div class="text-center">
                          <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            No employee linked
                          </small>
                          <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'employees.create')): ?>
                            <br>
                            <a href="<?= appUrl('employees/create.php') ?>"
                               class="btn btn-outline-primary btn-sm mt-1"
                               title="Create employee profile for this user">
                              <i class="bi bi-plus-circle me-1"></i>
                              <span class="d-none d-lg-inline">Link Employee</span>
                            </a>
                          <?php endif; ?>
                        </div>
                      <?php endif; ?>
                    </div>
                  </td>
                </tr>
              <?php endforeach; ?>
            </tbody>
          </table>

          <!-- Mobile Card View -->
          <div class="mobile-user-cards d-lg-none" id="mobileUserCards">
            <?php foreach ($users as $user): ?>
              <div class="mobile-user-card"
                   data-username="<?= strtolower(htmlspecialchars($user['username'])) ?>"
                   data-employee="<?= strtolower(htmlspecialchars($user['employee_name'] ?? '')) ?>"
                   data-email="<?= strtolower(htmlspecialchars($user['employee_email'] ?? '')) ?>"
                   data-department="<?= strtolower(htmlspecialchars($user['department'] ?? '')) ?>"
                   data-status="<?= strtolower($user['status']) ?>">

                <!-- Card Header (Always Visible) -->
                <div class="card-header" onclick="toggleMobileCard(this)">
                  <div class="d-flex align-items-center">
                    <div class="avatar me-3">
                      <?= strtoupper(substr($user['username'], 0, 1)) ?>
                    </div>
                    <div class="flex-grow-1">
                      <div class="fw-bold text-primary"><?= htmlspecialchars($user['username']) ?></div>
                      <?php if ($user['employee_name']): ?>
                        <small class="text-muted"><?= htmlspecialchars($user['employee_name']) ?></small>
                      <?php else: ?>
                        <small class="text-muted">No employee linked</small>
                      <?php endif; ?>
                    </div>
                    <div class="d-flex align-items-center">
                      <?php
                      $status_class = match($user['status']) {
                        'active' => 'success',
                        'inactive' => 'warning',
                        'suspended' => 'danger',
                        default => 'secondary'
                      };
                      $status_icon = match($user['status']) {
                        'active' => 'check-circle-fill',
                        'inactive' => 'pause-circle-fill',
                        'suspended' => 'x-circle-fill',
                        default => 'question-circle-fill'
                      };
                      $status_text = match($user['status']) {
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'suspended' => 'Suspended',
                        default => 'Unknown'
                      };
                      ?>
                      <span class="badge bg-<?= $status_class ?> me-2 px-2 py-1">
                        <i class="bi bi-<?= $status_icon ?> me-1"></i>
                        <?= $status_text ?>
                      </span>
                      <i class="bi bi-chevron-down expand-icon"></i>
                    </div>
                  </div>
                </div>

                <!-- Card Details (Expandable) -->
                <div class="card-details" style="display: none;">
                  <div class="row g-3">
                    <!-- User Account Details -->
                    <div class="col-12">
                      <h6 class="text-primary mb-2">
                        <i class="bi bi-person me-2"></i>
                        User Account
                      </h6>
                      <div class="detail-item">
                        <small class="text-muted">User ID:</small>
                        <span><?= $user['id'] ?></span>
                      </div>
                      <?php if ($user['employee_created_at']): ?>
                        <div class="detail-item">
                          <small class="text-muted">Joined:</small>
                          <span><?= date('M j, Y', strtotime($user['employee_created_at'])) ?></span>
                        </div>
                      <?php elseif ($user['hire_date']): ?>
                        <div class="detail-item">
                          <small class="text-muted">Hired:</small>
                          <span><?= date('M j, Y', strtotime($user['hire_date'])) ?></span>
                        </div>
                      <?php endif; ?>
                    </div>

                    <!-- Employee Details -->
                    <?php if ($user['employee_name']): ?>
                      <div class="col-12">
                        <h6 class="text-primary mb-2">
                          <i class="bi bi-person-badge me-2"></i>
                          Employee Details
                        </h6>
                        <?php if ($user['employee_email']): ?>
                          <div class="detail-item">
                            <small class="text-muted">Email:</small>
                            <span><?= htmlspecialchars($user['employee_email']) ?></span>
                          </div>
                        <?php endif; ?>
                        <?php if ($user['department']): ?>
                          <div class="detail-item">
                            <small class="text-muted">Department:</small>
                            <span class="badge bg-primary bg-opacity-10 text-primary border border-primary border-opacity-25">
                              <?= htmlspecialchars($user['department']) ?>
                            </span>
                          </div>
                        <?php endif; ?>
                        <?php if ($user['position']): ?>
                          <div class="detail-item">
                            <small class="text-muted">Job Title:</small>
                            <span class="badge bg-secondary bg-opacity-10 text-secondary border border-secondary border-opacity-25">
                              <i class="bi bi-briefcase me-1"></i>
                              <?= htmlspecialchars($user['position']) ?>
                            </span>
                          </div>
                        <?php endif; ?>
                      </div>
                    <?php endif; ?>

                    <!-- Status & System Access -->
                    <div class="col-12">
                      <h6 class="text-primary mb-2">
                        <i class="bi bi-shield-lock me-2"></i>
                        Status & System Access
                      </h6>
                      <div class="detail-item">
                        <small class="text-muted">Status:</small>
                        <span class="badge bg-<?= $status_class ?> px-2 py-1">
                          <i class="bi bi-<?= $status_icon ?> me-1"></i>
                          <?= $status_text ?>
                        </span>
                      </div>
                      <?php if ($user['roles']): ?>
                        <div class="detail-item">
                          <small class="text-muted">System Roles:</small>
                          <div class="roles-mobile">
                            <?php
                            $roles_array = explode(', ', $user['roles']);
                            foreach ($roles_array as $role):
                            ?>
                              <span class="badge bg-info bg-opacity-10 text-info border border-info border-opacity-25 me-1 mb-1">
                                <i class="bi bi-shield-lock me-1"></i>
                                <?= htmlspecialchars(trim($role)) ?>
                              </span>
                            <?php endforeach; ?>
                          </div>
                        </div>
                      <?php else: ?>
                        <div class="detail-item">
                          <small class="text-muted">System Roles:</small>
                          <small class="text-muted">
                            <i class="bi bi-exclamation-triangle me-1"></i>
                            No system roles assigned
                          </small>
                        </div>
                      <?php endif; ?>
                    </div>

                    <!-- Actions -->
                    <div class="col-12">
                      <h6 class="text-primary mb-2">
                        <i class="bi bi-gear me-2"></i>
                        Actions
                      </h6>
                      <div class="d-flex gap-2 flex-wrap">
                        <?php if ($user['employee_id']): ?>
                          <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'users.edit')): ?>
                            <a href="<?= appUrl('users/edit.php?id=' . $user['id']) ?>"
                               class="btn btn-primary btn-sm">
                              <i class="bi bi-key me-1"></i>
                              Edit User Account
                            </a>
                          <?php endif; ?>

                          <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'employees.edit')): ?>
                            <a href="<?= appUrl('employees/edit.php?id=' . $user['employee_id']) ?>"
                               class="btn btn-secondary btn-sm">
                              <i class="bi bi-person-gear me-1"></i>
                              Edit Employee
                            </a>
                          <?php elseif (hasPermission($mysqli, $_SESSION['user_id'], 'employees.view')): ?>
                            <a href="<?= appUrl('employees/edit.php?id=' . $user['employee_id']) ?>"
                               class="btn btn-info btn-sm">
                              <i class="bi bi-eye me-1"></i>
                              View Employee
                            </a>
                          <?php endif; ?>

                          <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'users.delete') && $user['id'] !== $_SESSION['user_id']): ?>
                            <form method="POST" class="d-inline">
                              <input type="hidden" name="user_id" value="<?= $user['id'] ?>" />
                              <button type="submit" name="delete_user"
                                      class="btn btn-danger btn-sm"
                                      onclick="return confirm('⚠️ Delete User Account\n\nAre you sure you want to delete user \'<?= htmlspecialchars($user['username']) ?>\'?\n\n• This will permanently remove the user account\n• Employee record will remain intact\n• This action cannot be undone\n\nClick OK to proceed or Cancel to abort.');">
                                <i class="bi bi-trash me-1"></i>
                                Delete User
                              </button>
                            </form>
                          <?php endif; ?>
                        <?php else: ?>
                          <div class="text-center w-100">
                            <small class="text-muted d-block mb-2">
                              <i class="bi bi-info-circle me-1"></i>
                              No employee linked
                            </small>
                            <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'employees.create')): ?>
                              <a href="<?= appUrl('employees/create.php') ?>"
                                 class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-plus-circle me-1"></i>
                                Link Employee
                              </a>
                            <?php endif; ?>
                          </div>
                        <?php endif; ?>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            <?php endforeach; ?>
          </div>
        </div>
      <?php endif; ?>
    </div>
  </div>

  <style>
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--primary-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 0.875rem;
      box-shadow: 0 1px 4px rgba(0,0,0,0.1);
      border: 1px solid rgba(255,255,255,0.2);
    }

    .hidden {
      display: none !important;
    }

    /* Enhanced table styling - Compact Design */
    .table {
      border-collapse: separate;
      border-spacing: 0;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
      font-size: 0.875rem;
    }

    .table thead th {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      font-weight: 600;
      border: none;
      padding: 0.75rem 0.875rem;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.3px;
    }

    .table tbody tr {
      transition: all 0.15s ease;
      border-bottom: 1px solid #f1f3f4;
    }

    .table tbody tr:hover {
      background-color: #f8f9ff;
    }

    .table tbody tr:last-child {
      border-bottom: none;
    }

    .table tbody td {
      padding: 0.75rem 0.875rem;
      vertical-align: middle;
      border: none;
      line-height: 1.4;
    }

    /* Badge enhancements - Compact */
    .badge {
      font-size: 0.7rem;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-weight: 500;
      line-height: 1.2;
    }

    /* Button enhancements - Compact */
    .btn-sm {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      border-radius: 4px;
      font-weight: 500;
      transition: all 0.15s ease;
      line-height: 1.3;
    }

    .btn-sm:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0,0,0,0.12);
    }

    /* Roles container */
    .roles-container {
      max-width: 200px;
    }

    .roles-container .badge {
      font-size: 0.7rem;
      padding: 0.25rem 0.5rem;
    }

    /* Mobile Card Styles - Compact */
    .mobile-user-cards {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      width: 100%;
      max-width: 100%;
      overflow-x: hidden;
    }

    .mobile-user-card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 1px 6px rgba(0,0,0,0.06);
      border: 1px solid #e9ecef;
      overflow: hidden;
      transition: all 0.2s ease;
      width: 100%;
      max-width: 100%;
      box-sizing: border-box;
    }

    .mobile-user-card:hover {
      box-shadow: 0 2px 12px rgba(0,0,0,0.1);
      transform: translateY(-1px);
    }

    .mobile-user-card .card-header {
      padding: 0.75rem;
      cursor: pointer;
      transition: background-color 0.15s ease;
      border-bottom: 1px solid transparent;
    }

    .mobile-user-card .card-header:hover {
      background-color: #f8f9ff;
    }

    .mobile-user-card.expanded .card-header {
      border-bottom-color: #e9ecef;
      background-color: #f8f9ff;
    }

    .mobile-user-card .expand-icon {
      transition: transform 0.3s ease;
      color: #6c757d;
    }

    .mobile-user-card.expanded .expand-icon {
      transform: rotate(180deg);
    }

    .mobile-user-card .card-details {
      padding: 0 0.75rem 0.75rem 0.75rem;
      border-top: 1px solid #f1f3f4;
      background-color: #fafbfc;
    }

    .mobile-user-card .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.375rem 0;
      border-bottom: 1px solid #f1f3f4;
      font-size: 0.875rem;
    }

    .mobile-user-card .detail-item:last-child {
      border-bottom: none;
    }

    .mobile-user-card .detail-item small {
      font-weight: 500;
      min-width: 70px;
      font-size: 0.8rem;
    }

    .mobile-user-card h6 {
      font-size: 0.8rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      padding-bottom: 0.375rem;
      border-bottom: 1px solid #e9ecef;
    }

    /* Responsive improvements - Compact */
    @media (max-width: 1200px) {
      .table tbody td {
        padding: 0.625rem 0.75rem;
      }

      .btn-sm span {
        display: none !important;
      }

      .btn-sm {
        padding: 0.25rem;
        min-width: 28px;
      }
    }

    @media (max-width: 992px) {
      .mobile-user-card .avatar {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
      }
    }

    @media (max-width: 768px) {
      .mobile-user-card {
        margin: 0;
        border-radius: 6px;
        width: 100%;
        max-width: 100%;
      }

      .mobile-user-card .card-header,
      .mobile-user-card .card-details {
        padding: 0.625rem;
        max-width: 100%;
        box-sizing: border-box;
      }

      .mobile-user-card .avatar {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
        flex-shrink: 0;
      }

      .badge {
        font-size: 0.65rem;
        padding: 0.2rem 0.4rem;
        max-width: 100%;
      }

      .btn-sm {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
      }

      .mobile-user-card .d-flex {
        flex-wrap: wrap;
        gap: 0.25rem;
      }

      .mobile-user-card .flex-grow-1 {
        min-width: 0;
        flex: 1 1 auto;
      }
    }

    /* Loading and empty states */
    .text-center.py-5 {
      padding: 3rem 1rem !important;
    }

    .text-center.py-5 i {
      opacity: 0.6;
    }

    /* Filter section enhancements - Compact */
    .content-card {
      border-radius: 8px;
      box-shadow: 0 1px 6px rgba(0,0,0,0.06);
      border: 1px solid #e9ecef;
    }

    /* Search and filter inputs - Compact */
    .form-control, .form-select {
      border-radius: 6px;
      border: 1px solid #dee2e6;
      transition: all 0.15s ease;
      font-size: 0.875rem;
      padding: 0.5rem 0.75rem;
    }

    .form-control:focus, .form-select:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 0.15rem rgba(102, 126, 234, 0.1);
    }

    /* User count display */
    #userCount {
      color: #667eea;
      font-weight: 700;
    }

    /* Additional compact styling */
    .content-card {
      padding: 1rem !important;
      margin-bottom: 1rem !important;
    }

    .table tbody td .fw-bold {
      font-size: 0.9rem;
    }

    .table tbody td small {
      font-size: 0.75rem;
    }

    .table tbody td .badge {
      font-size: 0.65rem;
      padding: 0.2rem 0.4rem;
    }

    /* Compact status badges in table */
    .table tbody td .badge.bg-success,
    .table tbody td .badge.bg-warning,
    .table tbody td .badge.bg-danger {
      padding: 0.3rem 0.6rem;
      font-size: 0.7rem;
    }

    /* Reduce icon sizes */
    .bi {
      font-size: 0.9rem;
    }

    /* Compact form elements */
    .form-label {
      font-size: 0.8rem;
      margin-bottom: 0.25rem;
    }

    /* Reduce gap between elements */
    .row.g-2 > * {
      padding-right: 0.5rem;
      padding-left: 0.5rem;
    }

    .row.g-3 > * {
      padding-right: 0.75rem;
      padding-left: 0.75rem;
    }

    /* Prevent horizontal overflow on mobile */
    @media (max-width: 991.98px) {
      body {
        overflow-x: hidden;
      }

      .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
        max-width: 100vw;
        overflow-x: hidden;
      }

      .content-card {
        margin-left: 0;
        margin-right: 0;
        max-width: 100%;
        overflow-x: hidden;
      }

      .mobile-user-card .card-header,
      .mobile-user-card .card-details {
        word-wrap: break-word;
        overflow-wrap: break-word;
        max-width: 100%;
      }

      .mobile-user-card .badge {
        max-width: 100%;
        word-break: break-word;
      }

      .mobile-user-card .detail-item {
        flex-wrap: wrap;
        gap: 0.25rem;
      }

      .mobile-user-card .detail-item small {
        min-width: auto;
        flex-shrink: 0;
      }
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Mobile card toggle functionality
    function toggleMobileCard(headerElement) {
      const card = headerElement.closest('.mobile-user-card');
      const details = card.querySelector('.card-details');
      const isExpanded = card.classList.contains('expanded');

      if (isExpanded) {
        // Collapse
        details.style.display = 'none';
        card.classList.remove('expanded');
      } else {
        // Expand
        details.style.display = 'block';
        card.classList.add('expanded');
      }
    }

    // Enhanced filter function for both desktop and mobile views
    function filterUsers() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const statusFilter = document.getElementById('statusFilter').value.toLowerCase();
      const departmentFilter = document.getElementById('departmentFilter').value.toLowerCase();

      // Filter desktop table rows
      const rows = document.querySelectorAll('.user-row');
      let visibleDesktopCount = 0;

      rows.forEach(row => {
        const username = row.dataset.username || '';
        const employee = row.dataset.employee || '';
        const email = row.dataset.email || '';
        const department = row.dataset.department || '';
        const status = row.dataset.status || '';

        const matchesSearch = !searchTerm ||
          username.includes(searchTerm) ||
          employee.includes(searchTerm) ||
          email.includes(searchTerm);

        const matchesStatus = !statusFilter || status === statusFilter;
        const matchesDepartment = !departmentFilter || department === departmentFilter;

        if (matchesSearch && matchesStatus && matchesDepartment) {
          row.classList.remove('hidden');
          visibleDesktopCount++;
        } else {
          row.classList.add('hidden');
        }
      });

      // Filter mobile cards
      const cards = document.querySelectorAll('.mobile-user-card');
      let visibleMobileCount = 0;

      cards.forEach(card => {
        const username = card.dataset.username || '';
        const employee = card.dataset.employee || '';
        const email = card.dataset.email || '';
        const department = card.dataset.department || '';
        const status = card.dataset.status || '';

        const matchesSearch = !searchTerm ||
          username.includes(searchTerm) ||
          employee.includes(searchTerm) ||
          email.includes(searchTerm);

        const matchesStatus = !statusFilter || status === statusFilter;
        const matchesDepartment = !departmentFilter || department === departmentFilter;

        if (matchesSearch && matchesStatus && matchesDepartment) {
          card.classList.remove('hidden');
          visibleMobileCount++;
        } else {
          card.classList.add('hidden');
          // Collapse expanded cards when hidden
          card.classList.remove('expanded');
          const details = card.querySelector('.card-details');
          if (details) details.style.display = 'none';
        }
      });

      // Update count (use desktop count as primary, mobile as fallback)
      const totalVisible = window.innerWidth >= 992 ? visibleDesktopCount : visibleMobileCount;
      document.getElementById('userCount').textContent = totalVisible;
    }

    // Handle window resize to update count appropriately
    function handleResize() {
      // Re-run filter to update count for current view
      filterUsers();
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      // Add resize listener
      window.addEventListener('resize', handleResize);

      // Add click outside to collapse mobile cards (optional UX enhancement)
      document.addEventListener('click', function(e) {
        if (window.innerWidth < 992) {
          const clickedCard = e.target.closest('.mobile-user-card');
          if (!clickedCard) {
            // Clicked outside any card, collapse all
            document.querySelectorAll('.mobile-user-card.expanded').forEach(card => {
              card.classList.remove('expanded');
              const details = card.querySelector('.card-details');
              if (details) details.style.display = 'none';
            });
          }
        }
      });

      // Prevent card collapse when clicking inside card details
      document.querySelectorAll('.card-details').forEach(details => {
        details.addEventListener('click', function(e) {
          e.stopPropagation();
        });
      });
    });
  </script>
</body>
</html>
