<?php
session_start();

// Include configuration and permissions system
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require employee creation permission
requirePermission($mysqli, 'employees.create');

$msg = "";
$error = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['add_employee'])) {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $department = trim($_POST['department'] ?? '');
    $position = trim($_POST['position'] ?? '');
    $hire_date = $_POST['hire_date'] ?? '';
    $create_account = isset($_POST['create_account']);
    $assigned_roles = $_POST['roles'] ?? [];

    if ($name) {
      // Start transaction
      $mysqli->begin_transaction();

      try {
        // Insert employee
        $stmt = $mysqli->prepare("INSERT INTO employees (name, email, phone, department, position, hire_date, status) VALUES (?, ?, ?, ?, ?, ?, 'active')");
        $stmt->bind_param('ssssss', $name, $email, $phone, $department, $position, $hire_date);

        if ($stmt->execute()) {
          $employee_id = $mysqli->insert_id;
          $stmt->close();

          // Create user account if requested
          if ($create_account) {
            $username = generateUsername($name);
            $password = generateSecurePassword();
            $password_hash = password_hash($password, PASSWORD_DEFAULT);

            // Check if username already exists
            $check_stmt = $mysqli->prepare("SELECT id FROM users WHERE username = ?");
            $check_stmt->bind_param('s', $username);
            $check_stmt->execute();
            $check_stmt->store_result();

            if ($check_stmt->num_rows > 0) {
              // Username exists, append employee ID
              $username = $username . '.' . $employee_id;
            }
            $check_stmt->close();

            // Insert user account
            $user_stmt = $mysqli->prepare("INSERT INTO users (employee_id, username, password_hash) VALUES (?, ?, ?)");
            $user_stmt->bind_param('iss', $employee_id, $username, $password_hash);

            if ($user_stmt->execute()) {
              $user_id = $mysqli->insert_id;
              $user_stmt->close();

              // Assign roles to user
              if (!empty($assigned_roles)) {
                foreach ($assigned_roles as $role_id) {
                  assignRoleToUser($mysqli, $user_id, intval($role_id), $_SESSION['user_id']);
                }
              }

              $mysqli->commit();
              $msg = "Employee added successfully! Username: $username, Password: $password (Please save this password securely)";
            } else {
              throw new Exception("Failed to create user account.");
            }
          } else {
            $mysqli->commit();
            $msg = "Employee added successfully!";
          }
        } else {
          throw new Exception("Failed to add employee.");
        }
      } catch (Exception $e) {
        $mysqli->rollback();
        $error = $e->getMessage();
      }
    } else {
      $error = "Name cannot be empty.";
    }
  }
}

// Get all available roles for the form
$available_roles = getAllRoles($mysqli);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Add Employee - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">


    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
        <div class="mt-2">
          <a href="<?= appUrl('employees/list.php') ?>" class="btn btn-sm btn-outline-success">View Employee List</a>
          <button type="button" class="btn btn-sm btn-outline-primary" onclick="resetForm()">Add Another Employee</button>
        </div>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Add Employee Form -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-person-plus me-2"></i>
        Employee Information
      </h2>
      <form method="POST" id="employeeForm">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="name" class="form-label">
                <i class="bi bi-person me-2"></i>
                Full Name *
              </label>
              <input
                type="text"
                class="form-control"
                name="name"
                id="name"
                required
                placeholder="Enter employee full name"
                value="<?= htmlspecialchars($_POST['name'] ?? '') ?>"
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="email" class="form-label">
                <i class="bi bi-envelope me-2"></i>
                Email Address
              </label>
              <input
                type="email"
                class="form-control"
                name="email"
                id="email"
                placeholder="<EMAIL>"
                value="<?= htmlspecialchars($_POST['email'] ?? '') ?>"
              />
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="phone" class="form-label">
                <i class="bi bi-telephone me-2"></i>
                Phone Number
              </label>
              <input
                type="tel"
                class="form-control"
                name="phone"
                id="phone"
                placeholder="+****************"
                value="<?= htmlspecialchars($_POST['phone'] ?? '') ?>"
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="hire_date" class="form-label">
                <i class="bi bi-calendar me-2"></i>
                Hire Date
              </label>
              <input
                type="date"
                class="form-control"
                name="hire_date"
                id="hire_date"
                value="<?= $_POST['hire_date'] ?? date('Y-m-d') ?>"
              />
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="department" class="form-label">
                <i class="bi bi-building me-2"></i>
                Department
              </label>
              <input
                type="text"
                class="form-control"
                name="department"
                id="department"
                placeholder="e.g., Operations, HR, IT"
                value="<?= htmlspecialchars($_POST['department'] ?? '') ?>"
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="position" class="form-label">
                <i class="bi bi-briefcase me-2"></i>
                Job Title
              </label>
              <input
                type="text"
                class="form-control"
                name="position"
                id="position"
                placeholder="e.g., Sales Associate, Team Lead, Customer Service Rep, Accountant"
                value="<?= htmlspecialchars($_POST['position'] ?? '') ?>"
              />
              <small class="form-text text-muted">
                <i class="bi bi-info-circle me-1"></i>
                The employee's specific job role or position within the company
              </small>
            </div>
          </div>
        </div>

        <!-- User Account Section -->
        <div class="form-section">
          <h5 class="section-title">
            <i class="bi bi-key me-2"></i>
            User Account Settings
          </h5>

          <div class="form-check mb-3">
            <input
              class="form-check-input"
              type="checkbox"
              name="create_account"
              id="create_account"
              <?= isset($_POST['create_account']) ? 'checked' : 'checked' ?>
              onchange="toggleAccountSettings()"
            />
            <label class="form-check-label" for="create_account">
              Create user account for this employee
            </label>
          </div>

          <div id="account_settings">
            <div class="form-group">
              <label class="form-label">
                <i class="bi bi-shield-check me-2"></i>
                System Access Roles
              </label>
              <small class="form-text text-muted mb-3 d-block">
                <i class="bi bi-info-circle me-1"></i>
                These roles control what features and pages the user can access in the system
              </small>
              <div class="roles-container">
                <?php foreach ($available_roles as $role): ?>
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      name="roles[]"
                      value="<?= $role['id'] ?>"
                      id="role_<?= $role['id'] ?>"
                      <?= $role['name'] === 'employee' ? 'checked' : '' ?>
                      <?= in_array($role['id'], $_POST['roles'] ?? []) ? 'checked' : '' ?>
                    />
                    <label class="form-check-label" for="role_<?= $role['id'] ?>">
                      <strong><?= htmlspecialchars($role['display_name']) ?></strong>
                      <?php if ($role['description']): ?>
                        <br><small class="text-muted"><?= htmlspecialchars($role['description']) ?></small>
                      <?php endif; ?>
                    </label>
                  </div>
                <?php endforeach; ?>
              </div>
            </div>

            <div class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i>
              <strong>Note:</strong> A secure password will be automatically generated.
              The username and password will be displayed after successful creation.
            </div>
          </div>
        </div>

        <div class="d-flex gap-3">
          <button type="submit" name="add_employee" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            Add Employee
          </button>
          <button type="button" class="btn btn-secondary" onclick="resetForm()">
            <i class="bi bi-arrow-clockwise me-2"></i>
            Reset Form
          </button>
          <a href="<?= appUrl('employees/list.php') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-x-circle me-2"></i>
            Cancel
          </a>
        </div>
      </form>
    </div>
  </div>

  <style>
    /* Compact Design Styles */
    .form-section {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 1.125rem;
      margin: 1.125rem 0;
      border: 1px solid #e9ecef;
    }

    .section-title {
      color: #495057;
      font-size: 1rem;
      margin-bottom: 0.75rem;
      padding-bottom: 0.375rem;
      border-bottom: 2px solid #dee2e6;
    }

    .roles-container {
      max-height: 180px;
      overflow-y: auto;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 0.875rem;
      background: white;
    }

    .roles-container .form-check {
      margin-bottom: 0.5rem;
      padding: 0.375rem;
      border-radius: 4px;
      transition: background-color 0.2s;
    }

    .roles-container .form-check:hover {
      background-color: #f8f9fa;
    }

    .form-group {
      margin-bottom: 1.125rem;
    }

    .form-label {
      font-weight: 500;
      font-size: 0.875rem;
      margin-bottom: 0.375rem;
      display: flex;
      align-items: center;
    }

    .form-control, .form-select {
      padding: 0.625rem 0.875rem;
      font-size: 0.875rem;
    }

    .form-text {
      font-size: 0.75rem;
      margin-top: 0.25rem;
    }

    .form-check-label {
      font-size: 0.875rem;
    }

    .form-check-label strong {
      font-size: 0.875rem;
    }

    .form-check-label small {
      font-size: 0.75rem;
    }

    .btn {
      padding: 0.625rem 1.25rem;
      font-size: 0.875rem;
    }

    .btn-group {
      gap: 0.375rem;
    }

    .alert {
      padding: 0.875rem 1.125rem;
      font-size: 0.875rem;
    }

    @media (max-width: 768px) {
      .form-section {
        padding: 1rem;
        margin: 1rem 0;
      }

      .d-flex.gap-3 {
        flex-direction: column;
        gap: 0.75rem !important;
      }

      .d-flex.gap-3 .btn {
        width: 100%;
        margin-bottom: 0;
      }

      .roles-container {
        max-height: 150px;
        padding: 0.75rem;
      }
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function toggleAccountSettings() {
      const checkbox = document.getElementById('create_account');
      const settings = document.getElementById('account_settings');

      if (checkbox.checked) {
        settings.style.display = 'block';
      } else {
        settings.style.display = 'none';
      }
    }

    function resetForm() {
      document.getElementById('employeeForm').reset();
      document.getElementById('hire_date').value = '<?= date('Y-m-d') ?>';
      document.getElementById('create_account').checked = true;

      // Reset employee role to checked
      const employeeRole = document.querySelector('input[name="roles[]"][value="<?php
        foreach($available_roles as $role) {
          if($role['name'] === 'employee') echo $role['id'];
        }
      ?>"]');
      if (employeeRole) {
        employeeRole.checked = true;
      }

      toggleAccountSettings();
    }

    // Initialize form state
    document.addEventListener('DOMContentLoaded', function() {
      toggleAccountSettings();
    });
  </script>
</body>
</html>
