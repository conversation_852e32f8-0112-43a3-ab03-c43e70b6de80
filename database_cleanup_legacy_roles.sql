-- Database Cleanup: Remove Legacy Role Columns
-- Run this script ONLY after confirming that the new roles and permissions system is working correctly
-- This script removes the legacy role columns from the users table

-- WARNING: This operation is irreversible. Make sure you have a backup of your database!

-- Step 1: Verify that the new roles and permissions system is working
-- Check if roles table exists and has data
SELECT 'Checking roles table...' as status;
SELECT COUNT(*) as role_count FROM roles;

-- Check if user_roles table exists and has data
SELECT 'Checking user_roles table...' as status;
SELECT COUNT(*) as user_role_assignments FROM user_roles;

-- Check if permissions table exists and has data
SELECT 'Checking permissions table...' as status;
SELECT COUNT(*) as permission_count FROM permissions;

-- Check if role_permissions table exists and has data
SELECT 'Checking role_permissions table...' as status;
SELECT COUNT(*) as role_permission_assignments FROM role_permissions;

-- Step 2: Show current users and their roles (for verification)
SELECT 'Current users and their new roles:' as status;
SELECT 
    u.id,
    u.username,
    u.role as legacy_role,
    u.old_role as backup_legacy_role,
    GROUP_CONCAT(r.display_name SEPARATOR ', ') as new_roles
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.id
GROUP BY u.id, u.username, u.role, u.old_role
ORDER BY u.username;

-- Step 3: Remove the legacy role columns
-- UNCOMMENT THE FOLLOWING LINES ONLY AFTER VERIFYING THE ABOVE CHECKS

-- Remove the old_role backup column
-- ALTER TABLE users DROP COLUMN IF EXISTS old_role;

-- Remove the legacy role column
-- ALTER TABLE users DROP COLUMN IF EXISTS role;

-- Step 4: Verify the cleanup
-- UNCOMMENT THE FOLLOWING LINES AFTER RUNNING THE DROP COLUMN COMMANDS

-- DESCRIBE users;

-- SELECT 'Legacy role columns removed successfully!' as status;

-- Final verification: Show users with their new roles only
-- SELECT 
--     u.id,
--     u.username,
--     GROUP_CONCAT(r.display_name SEPARATOR ', ') as roles
-- FROM users u
-- LEFT JOIN user_roles ur ON u.id = ur.user_id
-- LEFT JOIN roles r ON ur.role_id = r.id
-- GROUP BY u.id, u.username
-- ORDER BY u.username;
