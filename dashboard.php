<?php
session_start();

// Include permissions system
require_once __DIR__ . '/includes/permissions.php';

if (!isset($_SESSION['user_id'])) {
  header("Location: login.php");
  exit;
}

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions
requirePermission($mysqli, 'dashboard.view');

// Refresh session if there's a message (likely from super admin redirect)
if (isset($_GET['msg']) && strpos($_GET['msg'], 'Super admin') !== false) {
    refreshUserSession($mysqli);
}

$username = $_SESSION['username'];
$user_permissions = $_SESSION['permissions'] ?? [];
$user_roles = $_SESSION['roles'] ?? [];

// Check if user has admin privileges (no more legacy fallback)
$is_admin = in_array('super_admin', $user_roles) || in_array('admin', $user_roles);
$is_super_admin = in_array('super_admin', $user_roles);
$can_view_admin_stats = hasPermission($mysqli, $_SESSION['user_id'], 'dashboard.admin_stats') || $is_admin;
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Dashboard - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="style.css" />
  <style>
    :root {
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      --dark-bg: #1a1d29;
      --card-bg: #ffffff;
      --text-primary: #2d3748;
      --text-secondary: #718096;
      --border-color: #e2e8f0;
      --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: var(--text-primary);
      line-height: 1.6;
    }

    .dashboard-container {
      margin-left: 280px;
      padding: 1.5rem;
      padding-top: calc(70px + 1.5rem);
      min-height: 100vh;
      opacity: 0;
      transition: opacity 0.3s ease-in-out;
    }

    /* Welcome Header */
    .welcome-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      color: white;
      text-align: center;
      box-shadow: var(--shadow-md);
    }

    .welcome-title {
      font-size: 1.75rem;
      font-weight: 600;
      margin-bottom: 0.25rem;
    }

    .welcome-subtitle {
      opacity: 0.9;
      font-size: 0.95rem;
      margin: 0;
    }

    /* Stats Overview */
    .stats-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .stat-item {
      background: var(--card-bg);
      border-radius: 12px;
      padding: 1.5rem 1rem;
      text-align: center;
      border: 1px solid var(--border-color);
      transition: all 0.2s ease-in-out;
    }

    .stat-item:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .stat-number {
      display: block;
      font-size: 2rem;
      font-weight: 700;
      color: #667eea;
      margin-bottom: 0.25rem;
    }

    .stat-text {
      font-size: 0.85rem;
      color: var(--text-secondary);
      font-weight: 500;
    }

    /* Action Grid */
    .action-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .action-card {
      background: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 12px;
      padding: 1.5rem;
      text-decoration: none;
      color: inherit;
      transition: all 0.2s ease-in-out;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      min-height: 140px;
      position: relative;
      overflow: hidden;
    }

    .action-card:hover {
      transform: translateY(-3px);
      box-shadow: var(--shadow-lg);
      text-decoration: none;
      color: inherit;
    }

    .action-card.primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-color: #667eea;
    }

    .action-card.primary .action-icon {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }

    .action-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      background: #f1f5f9;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
      font-size: 1.25rem;
      color: #667eea;
      transition: all 0.2s ease-in-out;
    }

    .action-content h3 {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 0.25rem;
    }

    .action-content p {
      font-size: 0.8rem;
      color: var(--text-secondary);
      opacity: 0.8;
    }

    .action-card.primary .action-content p {
      color: rgba(255, 255, 255, 0.8);
    }



    /* Responsive Design */
    @media (max-width: 768px) {
      .dashboard-container {
        margin-left: 0;
        padding: 1rem;
        padding-top: calc(70px + 1rem);
      }

      .welcome-header {
        padding: 1.25rem;
      }

      .welcome-title {
        font-size: 1.5rem;
      }

      .stats-overview {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 0.75rem;
      }

      .stat-item {
        padding: 1rem 0.75rem;
      }

      .stat-number {
        font-size: 1.5rem;
      }

      .action-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }

      .action-card {
        min-height: 120px;
        padding: 1.25rem;
      }

      .action-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
      }
    }

    @media (max-width: 480px) {
      .dashboard-container {
        margin-left: 0;
        padding: 0.75rem;
        padding-top: calc(70px + 0.75rem);
      }

      .welcome-header {
        padding: 1rem;
        margin-bottom: 1.5rem;
      }

      .welcome-title {
        font-size: 1.25rem;
      }

      .welcome-subtitle {
        font-size: 0.85rem;
      }

      .stats-overview {
        margin-bottom: 1.5rem;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.5rem;
      }

      .stat-item {
        padding: 0.75rem 0.5rem;
      }

      .stat-number {
        font-size: 1.25rem;
      }

      .stat-text {
        font-size: 0.75rem;
      }

      .action-card {
        min-height: 100px;
        padding: 1rem;
      }

      .action-content h3 {
        font-size: 0.9rem;
      }

      .action-content p {
        font-size: 0.75rem;
      }
    }

    /* Animations */
    .animate-fade-in {
      animation: fadeIn 0.5s ease-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Loading state */
    .loading {
      opacity: 0;
    }

    /* Focus styles for accessibility */
    .action-card:focus {
      outline: 2px solid #667eea;
      outline-offset: 2px;
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      :root {
        --card-bg: #1e293b;
        --text-primary: #f1f5f9;
        --text-secondary: #94a3b8;
        --border-color: #334155;
      }

      body {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
      }
    }
  </style>
</head>
<body>
  <?php include 'includes/navigation.php'; ?>

  <div class="dashboard-container loading animate-fade-in">
    <!-- Message Display -->
    <?php if (isset($_GET['msg'])): ?>
      <div class="alert alert-info mb-3">
        <i class="bi bi-info-circle me-2"></i>
        <?= htmlspecialchars(urldecode($_GET['msg'])) ?>
      </div>
    <?php endif; ?>

    <!-- Refresh Session Message -->
    <?php if (isset($_GET['refresh_msg'])): ?>
      <div class="alert alert-success mb-3">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars(urldecode($_GET['refresh_msg'])) ?>
      </div>
    <?php endif; ?>

    <!-- Simplified Welcome Section -->
    <div class="welcome-header">
      <div class="welcome-content">
        <h1 class="welcome-title">
          <?php
          $hour = date('H');
          $greeting = $hour < 12 ? 'Good morning' : ($hour < 17 ? 'Good afternoon' : 'Good evening');
          echo $greeting . ', ' . htmlspecialchars($username) . '!';
          ?>
        </h1>
      </div>
    </div>

    <?php if ($can_view_admin_stats): ?>
      <!-- Admin Statistics -->
      <div class="stats-overview">
        <?php
        $employees_count = $mysqli->query("SELECT COUNT(*) as count FROM employees")->fetch_assoc()['count'];
        $shifts_count = $mysqli->query("SELECT COUNT(*) as count FROM shift_templates")->fetch_assoc()['count'];
        $today = date('Y-m-d');
        $checkins_count = $mysqli->query("SELECT COUNT(*) as count FROM attendance WHERE DATE(timestamp) = '$today' AND type = 'in'")->fetch_assoc()['count'];
        $checkouts_count = $mysqli->query("SELECT COUNT(*) as count FROM attendance WHERE DATE(timestamp) = '$today' AND type = 'out'")->fetch_assoc()['count'];
        $users_count = $mysqli->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
        ?>

        <div class="stat-item">
          <span class="stat-number"><?= $employees_count ?></span>
          <span class="stat-text">Total Employees</span>
        </div>
        <div class="stat-item">
          <span class="stat-number"><?= $users_count ?></span>
          <span class="stat-text">User Accounts</span>
        </div>
        <div class="stat-item">
          <span class="stat-number"><?= $shifts_count ?></span>
          <span class="stat-text">Shift Templates</span>
        </div>
        <div class="stat-item">
          <span class="stat-number"><?= $checkins_count ?></span>
          <span class="stat-text">Today's Check-ins</span>
        </div>
        <div class="stat-item">
          <span class="stat-number"><?= $checkouts_count ?></span>
          <span class="stat-text">Today's Check-outs</span>
        </div>
      </div>
    <?php endif; ?>

    <!-- Main Actions -->
    <div class="action-grid">
      <!-- Clock In/Out - Available to employees (excluding super admins) -->
      <?php if (hasAnyPermission($mysqli, $_SESSION['user_id'], ['attendance.clock_in', 'attendance.clock_out']) && !$is_super_admin): ?>
        <a href="index.php" class="action-card primary" onclick="showLoading()">
          <div class="action-icon">
            <i class="bi bi-check2-circle"></i>
          </div>
          <div class="action-content">
            <h3>Clock In/Out</h3>
            <p>Record attendance</p>
          </div>
        </a>
      <?php endif; ?>

      <!-- Employee Management -->
      <?php if (hasAnyPermission($mysqli, $_SESSION['user_id'], ['employees.view', 'employees.create', 'employees.edit'])): ?>
        <a href="manage_employees.php" class="action-card" onclick="showLoading()">
          <div class="action-icon">
            <i class="bi bi-people-fill"></i>
          </div>
          <div class="action-content">
            <h3>Employees</h3>
            <p>Manage your team</p>
          </div>
        </a>
      <?php endif; ?>

      <!-- Shift Management -->
      <?php if (hasAnyPermission($mysqli, $_SESSION['user_id'], ['shifts.view', 'shifts.create', 'shifts.edit'])): ?>
        <a href="shifts/templates.php" class="action-card" onclick="showLoading()">
          <div class="action-icon">
            <i class="bi bi-clock-fill"></i>
          </div>
          <div class="action-content">
            <h3>Shift Templates</h3>
            <p>Manage shift templates</p>
          </div>
        </a>
      <?php endif; ?>

      <!-- Shift Assignments -->
      <?php if (hasAnyPermission($mysqli, $_SESSION['user_id'], ['shifts.view'])): ?>
        <a href="shifts/assignments.php" class="action-card" onclick="showLoading()">
          <div class="action-icon">
            <i class="bi bi-calendar-check"></i>
          </div>
          <div class="action-content">
            <h3>Shift Assignments</h3>
            <p>View current assignments</p>
          </div>
        </a>
      <?php endif; ?>

      <!-- Assign Shifts -->
      <?php if (hasAnyPermission($mysqli, $_SESSION['user_id'], ['shifts.assign'])): ?>
        <a href="shifts/assign.php" class="action-card" onclick="showLoading()">
          <div class="action-icon">
            <i class="bi bi-person-plus"></i>
          </div>
          <div class="action-content">
            <h3>Assign Shifts</h3>
            <p>Assign shifts to employees</p>
          </div>
        </a>
      <?php endif; ?>

      <!-- Break Management -->
      <?php if (hasAnyPermission($mysqli, $_SESSION['user_id'], ['breaks.view', 'breaks.create', 'breaks.edit'])): ?>
        <a href="manage_breaks.php" class="action-card" onclick="showLoading()">
          <div class="action-icon">
            <i class="bi bi-pause-circle-fill"></i>
          </div>
          <div class="action-content">
            <h3>Breaks</h3>
            <p>Break management</p>
          </div>
        </a>
      <?php endif; ?>

      <!-- Reports -->
      <?php if (hasAnyPermission($mysqli, $_SESSION['user_id'], ['reports.view', 'reports.view_all'])): ?>
        <a href="reports.php" class="action-card" onclick="showLoading()">
          <div class="action-icon">
            <i class="bi bi-bar-chart-fill"></i>
          </div>
          <div class="action-content">
            <h3>Reports</h3>
            <p>Analytics & insights</p>
          </div>
        </a>
      <?php endif; ?>

      <!-- Roles & Permissions -->
      <?php if (hasAnyPermission($mysqli, $_SESSION['user_id'], ['roles.view', 'roles.create', 'roles.edit'])): ?>
        <a href="manage_roles.php" class="action-card" onclick="showLoading()">
          <div class="action-icon">
            <i class="bi bi-shield-check"></i>
          </div>
          <div class="action-content">
            <h3>Roles & Permissions</h3>
            <p>Access control</p>
          </div>
        </a>
      <?php endif; ?>

      <!-- User Management -->
      <?php if (hasAnyPermission($mysqli, $_SESSION['user_id'], ['users.view', 'users.create', 'users.edit'])): ?>
        <a href="manage_users.php" class="action-card" onclick="showLoading()">
          <div class="action-icon">
            <i class="bi bi-person-gear"></i>
          </div>
          <div class="action-content">
            <h3>User Accounts</h3>
            <p>Account management</p>
          </div>
        </a>
      <?php endif; ?>
    </div>
  </div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
  // Enhanced Dashboard Functionality
  document.addEventListener('DOMContentLoaded', function() {
    // Add loading animation
    setTimeout(() => {
      document.querySelector('.dashboard-container').style.opacity = '1';
    }, 100);



    // Add click animations to action cards
    const cards = document.querySelectorAll('.action-card');
    cards.forEach(card => {
      card.addEventListener('click', function(e) {
        // Create ripple effect
        const ripple = document.createElement('div');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.cssText = `
          position: absolute;
          width: ${size}px;
          height: ${size}px;
          left: ${x}px;
          top: ${y}px;
          background: rgba(102, 126, 234, 0.2);
          border-radius: 50%;
          transform: scale(0);
          animation: ripple 0.4s ease-out;
          pointer-events: none;
          z-index: 1;
        `;

        this.style.position = 'relative';
        this.appendChild(ripple);

        setTimeout(() => {
          ripple.remove();
        }, 400);
      });
    });

    // Animate stat numbers on load
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
      const finalValue = parseInt(stat.textContent);
      let currentValue = 0;
      const increment = finalValue / 20;

      const timer = setInterval(() => {
        currentValue += increment;
        if (currentValue >= finalValue) {
          stat.textContent = finalValue;
          clearInterval(timer);
        } else {
          stat.textContent = Math.floor(currentValue);
        }
      }, 50);
    });

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        // Focus on first action card
        const firstCard = document.querySelector('.action-card');
        if (firstCard) firstCard.focus();
      }
    });

    // Add focus styles for accessibility
    cards.forEach(card => {
      card.addEventListener('focus', function() {
        this.style.outline = '2px solid #667eea';
        this.style.outlineOffset = '2px';
      });

      card.addEventListener('blur', function() {
        this.style.outline = 'none';
      });
    });
  });

  // Add CSS for ripple animation
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ripple {
      to {
        transform: scale(2);
        opacity: 0;
      }
    }

    .action-card {
      overflow: hidden;
    }

    .action-card:focus {
      outline: 2px solid #667eea !important;
      outline-offset: 2px !important;
    }
  `;
  document.head.appendChild(style);
</script>
</body>
</html>
