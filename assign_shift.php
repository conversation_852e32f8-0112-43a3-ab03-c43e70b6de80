<?php
require_once __DIR__ . '/includes/attendance_helpers.php';
require_once __DIR__ . '/includes/permissions.php';

session_start();
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php");
  exit;
}

$host = "localhost";
$user = "root";
$pass = "";
$db = "attendance_db2";

$mysqli = new mysqli($host, $user, $pass, $db);
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check if user has permission to assign shifts
if (!hasPermission($mysqli, $_SESSION['user_id'], 'shifts.assign') && !hasPermission($mysqli, $_SESSION['user_id'], 'shifts.create')) {
  header("Location: dashboard.php?error=" . urlencode("You don't have permission to assign shifts."));
  exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  $employee_id = intval($_POST['employee_id'] ?? 0);
  $shift_date = $_POST['shift_date'] ?? '';
  $start_time = $_POST['start_time'] ?? '';
  $end_time = $_POST['end_time'] ?? '';

  if ($employee_id && $shift_date && $start_time && $end_time) {
    $stmt = $mysqli->prepare("SELECT id FROM shifts WHERE employee_id = ? AND shift_date = ?");
    $stmt->bind_param('is', $employee_id, $shift_date);
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows > 0) {
      $stmt->close();
      $stmt = $mysqli->prepare("UPDATE shifts SET start_time = ?, end_time = ? WHERE employee_id = ? AND shift_date = ?");
      $stmt->bind_param('ssis', $start_time, $end_time, $employee_id, $shift_date);
      $stmt->execute();
      $stmt->close();
      $msg = "Shift updated.";
    } else {
      $stmt->close();
      $stmt = $mysqli->prepare("INSERT INTO shifts (employee_id, shift_date, start_time, end_time) VALUES (?, ?, ?, ?)");
      $stmt->bind_param('isss', $employee_id, $shift_date, $start_time, $end_time);
      $stmt->execute();
      $stmt->close();
      $msg = "Shift assigned.";
    }
  } else {
    $msg = "Please fill all fields.";
  }
}

$result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
$employees = [];
while ($row = $result->fetch_assoc()) {
  $employees[] = $row;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Assign Shift</title>
<style>
body { font-family: Arial, sans-serif; max-width: 500px; margin: 20px auto; padding: 0 10px; }
label { display: block; margin-top: 10px; font-weight: bold; }
input, select { width: 100%; padding: 8px; margin-top: 4px; }
button { margin-top: 20px; padding: 12px; width: 100%; background-color: #28a745; color: white; border: none; border-radius: 6px; font-size: 16px; }
.message { margin-top: 10px; color: green; }
</style>
</head>
<body>
<h1>Assign Shift</h1>

<?php if (!empty($msg)) echo '<p class="message">' . htmlspecialchars($msg) . '</p>'; ?>

<form method="POST" action="assign_shift.php">
  <label for="employee_id">Employee:</label>
  <select name="employee_id" id="employee_id" required>
    <option value="">Select employee</option>
    <?php foreach($employees as $emp): ?>
      <option value="<?= $emp['id'] ?>"><?= htmlspecialchars($emp['name']) ?></option>
    <?php endforeach; ?>
  </select>

  <label for="shift_date">Date:</label>
  <input type="date" name="shift_date" id="shift_date" required />

  <label for="start_time">Start Time:</label>
  <input type="time" name="start_time" id="start_time" required />

  <label for="end_time">End Time:</label>
  <input type="time" name="end_time" id="end_time" required />

  <button type="submit">Assign Shift</button>
</form>

<p><a href="index.html">Back to clock in/out</a></p>
</body>
</html>
