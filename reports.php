<?php
session_start();
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php");
  exit;
}

require_once __DIR__ . '/includes/attendance_helpers.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

$role = $_SESSION['role'];
$employee_id_session = $_SESSION['employee_id'] ?? 0;

// Fetch employees for dropdown (admin only)
$employees = [];
$result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
while ($row = $result->fetch_assoc()) {
  $employees[] = $row;
}

// Selected employee/date
if ($role === 'admin') {
  $selected_employee = isset($_GET['employee_id']) ? intval($_GET['employee_id']) : 0;
  if (!$selected_employee && !empty($employees)) {
    $selected_employee = $employees[0]['id'];
  }
} else {
  $selected_employee = $employee_id_session;
}
$selected_date = $_GET['date'] ?? date('Y-m-d');

// Retrieve assigned shifts for this employee/date (date in assigned range)
$stmt = $mysqli->prepare("
  SELECT st.id, st.name, st.start_time, st.end_time
  FROM employee_shifts es
  JOIN shift_templates st ON es.shift_template_id = st.id
  WHERE es.employee_id = ? AND ? BETWEEN es.shift_start_date AND es.shift_end_date
");
$stmt->bind_param("is", $selected_employee, $selected_date);
$stmt->execute();
$stmt->bind_result($st_id, $st_name, $st_start, $st_end);

$shiftAssignments = [];
while ($stmt->fetch()) {
  $shiftAssignments[] = [
    'id' => $st_id,
    'name' => $st_name,
    'start' => $st_start,
    'end' => $st_end,
    'breaks' => [],
  ];
}
$stmt->close();

foreach ($shiftAssignments as &$shift) {
  $shift['breaks'] = getShiftBreaks($mysqli, $shift['id']);
}
unset($shift);

// Fetch attendance records
$stmt = $mysqli->prepare("
  SELECT type, timestamp, selfie_path
  FROM attendance
  WHERE employee_id = ? AND DATE(timestamp) = ?
  ORDER BY timestamp ASC
");
$stmt->bind_param('is', $selected_employee, $selected_date);
$stmt->execute();
$stmt->bind_result($type, $timestamp, $selfie_path);

$attendanceRecords = [];
while ($stmt->fetch()) {
  $attendanceRecords[] = [
    'type' => $type,
    'timestamp' => $timestamp,
    'selfie' => $selfie_path,
  ];
}
$stmt->close();

$attendanceAnalysis = analyzeAttendance($shiftAssignments, $attendanceRecords);

$selectedEmployeeName = '';
foreach ($employees as $emp) {
  if ($emp['id'] === $selected_employee) {
    $selectedEmployeeName = $emp['name'];
    break;
  }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Attendance Reports - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <div class="page-container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Attendance Reports</h1>
          <p class="page-subtitle">View detailed attendance records and analytics</p>
        </div>
        <div>
          <a href="dashboard.php" class="nav-link">
            <i class="bi bi-arrow-left"></i>
            Back to Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Filter Form -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-funnel me-2"></i>
        Report Filters
      </h2>
      <form method="GET" action="reports.php" class="row g-3">
        <?php if ($role === 'admin'): ?>
          <div class="col-md-6">
            <label for="employee_id" class="form-label">
              <i class="bi bi-person me-2"></i>
              Select Employee
            </label>
            <select id="employee_id" name="employee_id" class="form-select" required>
              <?php foreach ($employees as $emp): ?>
                <option value="<?= $emp['id'] ?>" <?= $emp['id'] == $selected_employee ? 'selected' : '' ?>>
                  <?= htmlspecialchars($emp['name']) ?>
                </option>
              <?php endforeach; ?>
            </select>
          </div>
        <?php endif; ?>

        <div class="col-md-<?= $role === 'admin' ? '4' : '8' ?>">
          <label for="date" class="form-label">
            <i class="bi bi-calendar me-2"></i>
            Select Date
          </label>
          <input type="date" id="date" name="date" value="<?= htmlspecialchars($selected_date) ?>" class="form-control" required />
        </div>

        <div class="col-md-2 d-flex align-items-end">
          <button type="submit" class="btn btn-primary w-100">
            <i class="bi bi-search me-2"></i>
            View Report
          </button>
        </div>
      </form>
    </div>

    <!-- Report Results -->
    <?php if (!$selected_employee): ?>
      <div class="alert alert-info">
        <i class="bi bi-info-circle me-2"></i>
        Please select an employee to view their attendance report.
      </div>
    <?php else: ?>

      <!-- Report Header -->
      <div class="content-card">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h2 class="card-title mb-0">
            <i class="bi bi-file-text me-2"></i>
            Report for <?= htmlspecialchars($selectedEmployeeName) ?>
          </h2>
          <span class="badge bg-primary"><?= htmlspecialchars($selected_date) ?></span>
        </div>

        <?php if (empty($shiftAssignments)): ?>
          <div class="alert alert-info">
            <i class="bi bi-calendar-x me-2"></i>
            No shift assigned for this date.
          </div>
        <?php else: ?>

          <!-- Attendance Analysis -->
          <?php foreach ($attendanceAnalysis as $shiftResult): ?>
            <div class="attendance-report-card">
              <div class="report-header">
                <h4 class="shift-name">
                  <i class="bi bi-clock me-2"></i>
                  <?= htmlspecialchars($shiftResult['shift_name']) ?>
                </h4>
                <span class="status-badge status-<?= strtolower(str_replace(' ', '-', $shiftResult['status'])) ?>">
                  <?= htmlspecialchars($shiftResult['status']) ?>
                </span>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="time-info">
                    <div class="time-entry">
                      <i class="bi bi-box-arrow-in-right text-success me-2"></i>
                      <strong>Clock In:</strong>
                      <?= $shiftResult['clock_in'] ? htmlspecialchars($shiftResult['clock_in']['timestamp']) : '<em class="text-muted">Not recorded</em>' ?>
                    </div>
                    <div class="time-entry">
                      <i class="bi bi-box-arrow-right text-danger me-2"></i>
                      <strong>Clock Out:</strong>
                      <?= $shiftResult['clock_out'] ? htmlspecialchars($shiftResult['clock_out']['timestamp']) : '<em class="text-muted">Not recorded</em>' ?>
                    </div>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="stats-grid">
                    <div class="stat-item">
                      <i class="bi bi-stopwatch text-primary"></i>
                      <div>
                        <div class="stat-value"><?= intval($shiftResult['work_minutes']) ?> min</div>
                        <div class="stat-label">Work Time</div>
                      </div>
                    </div>
                    <div class="stat-item">
                      <i class="bi bi-clock-history text-warning"></i>
                      <div>
                        <div class="stat-value"><?= intval($shiftResult['overtime']) ?> min</div>
                        <div class="stat-label">Overtime</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Selfie Images -->
              <?php if (($shiftResult['clock_in'] && $shiftResult['clock_in']['selfie']) || ($shiftResult['clock_out'] && $shiftResult['clock_out']['selfie'])): ?>
                <div class="selfie-section">
                  <h6 class="mb-3">
                    <i class="bi bi-camera me-2"></i>
                    Verification Photos
                  </h6>
                  <div class="selfie-gallery">
                    <?php if ($shiftResult['clock_in'] && $shiftResult['clock_in']['selfie']): ?>
                      <div class="selfie-item">
                        <img src="<?= htmlspecialchars($shiftResult['clock_in']['selfie']) ?>" alt="Clock In Selfie" class="selfie-img" />
                        <div class="selfie-label">Clock In</div>
                      </div>
                    <?php endif; ?>
                    <?php if ($shiftResult['clock_out'] && $shiftResult['clock_out']['selfie']): ?>
                      <div class="selfie-item">
                        <img src="<?= htmlspecialchars($shiftResult['clock_out']['selfie']) ?>" alt="Clock Out Selfie" class="selfie-img" />
                        <div class="selfie-label">Clock Out</div>
                      </div>
                    <?php endif; ?>
                  </div>
                </div>
              <?php endif; ?>

              <!-- Breaks -->
              <?php if (!empty($shiftResult['breaks'])): ?>
                <div class="breaks-section">
                  <h6 class="mb-3">
                    <i class="bi bi-pause-circle me-2"></i>
                    Scheduled Breaks
                  </h6>
                  <div class="breaks-list">
                    <?php foreach ($shiftResult['breaks'] as $break): ?>
                      <div class="break-item">
                        <i class="bi bi-clock me-2"></i>
                        <?= htmlspecialchars($break['start']) ?> - <?= htmlspecialchars($break['end']) ?>
                      </div>
                    <?php endforeach; ?>
                  </div>
                </div>
              <?php endif; ?>
            </div>
          <?php endforeach; ?>

        <?php endif; ?>
      </div>
    <?php endif; ?>
  </div>

  <style>
    /* Compact Design Styles */
    .attendance-report-card {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 1.125rem;
      margin-bottom: 1.125rem;
      border: 1px solid var(--border-color);
    }

    .report-header {
      display: flex;
      justify-content: between;
      align-items: center;
      margin-bottom: 1.125rem;
      flex-wrap: wrap;
      gap: 0.75rem;
    }

    .shift-name {
      margin: 0;
      color: var(--text-primary);
      font-size: 1.125rem;
    }

    .status-badge {
      padding: 0.375rem 0.875rem;
      border-radius: 16px;
      font-weight: 600;
      font-size: 0.8rem;
    }

    .status-on-time { background: #d1ecf1; color: #0c5460; }
    .status-early { background: #d4edda; color: #155724; }
    .status-late { background: #f8d7da; color: #721c24; }
    .status-absent { background: #e2e3e5; color: #383d41; }

    .time-info {
      background: white;
      border-radius: 8px;
      padding: 0.875rem;
      border: 1px solid var(--border-color);
    }

    .time-entry {
      margin-bottom: 0.625rem;
      display: flex;
      align-items: center;
      font-size: 0.875rem;
    }

    .time-entry:last-child {
      margin-bottom: 0;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0.75rem;
    }

    .stat-item {
      background: white;
      border-radius: 8px;
      padding: 0.875rem;
      border: 1px solid var(--border-color);
      display: flex;
      align-items: center;
      gap: 0.625rem;
    }

    .stat-item i {
      font-size: 1.25rem;
    }

    .stat-value {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    .stat-label {
      font-size: 0.8rem;
      color: var(--text-secondary);
    }

    .selfie-section, .breaks-section {
      margin-top: 1.125rem;
      padding-top: 1.125rem;
      border-top: 1px solid var(--border-color);
    }

    .selfie-section h6, .breaks-section h6 {
      font-size: 1rem;
      margin-bottom: 0.75rem;
    }

    .selfie-gallery {
      display: flex;
      gap: 0.875rem;
      flex-wrap: wrap;
    }

    .selfie-item {
      text-align: center;
    }

    .selfie-img {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 10px;
      border: 2px solid var(--border-color);
      box-shadow: var(--shadow-sm);
    }

    .selfie-label {
      margin-top: 0.375rem;
      font-size: 0.8rem;
      color: var(--text-secondary);
      font-weight: 500;
    }

    .breaks-list {
      display: flex;
      flex-wrap: wrap;
      gap: 0.625rem;
    }

    .break-item {
      background: white;
      padding: 0.375rem 0.875rem;
      border-radius: 16px;
      border: 1px solid var(--border-color);
      font-size: 0.8rem;
      display: flex;
      align-items: center;
    }

    .form-group {
      margin-bottom: 1rem;
    }

    .form-label {
      font-size: 0.875rem;
      margin-bottom: 0.375rem;
    }

    .form-control, .form-select {
      padding: 0.625rem 0.875rem;
      font-size: 0.875rem;
    }

    .btn {
      padding: 0.625rem 1.25rem;
      font-size: 0.875rem;
    }

    @media (max-width: 768px) {
      .attendance-report-card {
        padding: 1rem;
        margin-bottom: 1rem;
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.625rem;
      }

      .report-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.625rem;
      }

      .selfie-img {
        width: 80px;
        height: 80px;
      }

      .selfie-gallery {
        gap: 0.625rem;
      }

      .form-group {
        margin-bottom: 0.875rem;
      }
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
